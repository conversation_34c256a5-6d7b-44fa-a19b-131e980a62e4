<template>
  <view class="evaluate-detail-page">
    <!-- 顶部提示栏和商品卡片组合 -->
    <view class="top-card-container">
      <!-- 顶部提示栏 -->
      <view class="top-notice">
        <text class="notice-text">极速回收 质检完成后秒到账</text>
        <text class="notice-right">极速专属</text>
      </view>

      <!-- 商品信息卡片 -->
      <view class="product-card">
      <view class="product-info">
        <view class="product-image">
          <image v-if="productInfo.image" :src="img(productInfo.image)" class="product-img" mode="aspectFill" />
          <view v-else class="image-placeholder">
            <text class="placeholder-text">{{ productLoading ? '加载中...' : '商品图片' }}</text>
          </view>
        </view>
        <view class="product-details">
          <text class="product-name">{{ productInfo.name || (productLoading ? '加载中...' : '商品名称') }}</text>
          <text class="product-code" v-if="productInfo.code">{{ productInfo.code }}</text>
        </view>
      </view>

      <!-- 价格选项 -->
      <view class="price-options-container">
        <view class="price-options-header">
          <text class="options-title">选择成色</text>
          <view class="condition-explain-btn" @click="showConditionModal">
            <text class="explain-icon">?</text>
            <text class="explain-text">成色说明</text>
          </view>
        </view>

        <view class="price-options">
          <view
            class="price-option"
            :class="{ active: selectedPriceType === 'new', disabled: productLoading }"
            @click="!productLoading && selectPriceType('new')"
          >
            <text class="option-label">全新未穿着</text>
            <view class="price-display" v-if="!productLoading">
              <view v-if="selectedPriceType === 'new' && voucherDiscount > 0" class="voucher-price">
                <text class="voucher-label">券后</text>
                <text class="voucher-amount">¥{{ finalPrice }}</text>
              </view>
              <text v-else class="normal-price">¥{{ priceData.new }}</text>
            </view>
            <text class="loading-text" v-else>加载中...</text>
          </view>
          <view
            class="price-option"
            :class="{ active: selectedPriceType === 'light', disabled: productLoading }"
            @click="!productLoading && selectPriceType('light')"
          >
            <text class="option-label">轻微穿着</text>
            <view class="price-display" v-if="!productLoading">
              <view v-if="selectedPriceType === 'light' && voucherDiscount > 0" class="voucher-price">
                <text class="voucher-label">券后</text>
                <text class="voucher-amount">¥{{ finalPrice }}</text>
              </view>
              <text v-else class="normal-price">¥{{ priceData.light }}</text>
            </view>
            <text class="loading-text" v-else>加载中...</text>
          </view>
          <view
            class="price-option"
            :class="{ active: selectedPriceType === 'obvious', disabled: productLoading }"
            @click="!productLoading && selectPriceType('obvious')"
          >
            <text class="option-label">明显穿着</text>
            <view class="price-display" v-if="!productLoading">
              <view v-if="selectedPriceType === 'obvious' && voucherDiscount > 0" class="voucher-price">
                <text class="voucher-label">券后</text>
                <text class="voucher-amount">¥{{ finalPrice }}</text>
              </view>
              <text v-else class="normal-price">¥{{ priceData.obvious }}</text>
            </view>
            <text class="loading-text" v-else>加载中...</text>
          </view>

        </view>
      </view>

      <!-- 价格明细 -->
      <view class="price-summary">
        <text class="summary-text">评估金额：¥{{ currentPrice }}</text>
        <view v-if="voucherDiscount > 0" class="voucher-addition">
          <text class="plus-symbol">+</text>
          <text class="bonus-text">加价券：¥{{ voucherDiscount }}</text>
          <view class="time-limit" v-if="selectedVoucherInfo?.voucher_info?.is_limited">
            <text class="limit-text">限时</text>
          </view>
        </view>
      </view>

      <!-- 优惠券选择 -->
      <view v-if="hasCoupon || couponList.length > 0" class="voucher-select-btn" @click="showCouponModal">
        <view class="btn-content">
          <text class="btn-title" v-if="voucherDiscount > 0">已选择加价券</text>
          <text class="btn-title" v-else-if="availableVouchers.length > 0">选择加价券</text>
          <text class="btn-title" v-else>查看加价券</text>

          <text class="btn-desc" v-if="voucherDiscount > 0">{{ selectedVoucherInfo?.voucher_info?.title || selectedVoucherInfo?.voucher_info?.name }}</text>
          <text class="btn-desc" v-else-if="availableVouchers.length > 0">{{ availableVouchers.length }}张可用，最高可加价¥{{ Math.max(...availableVouchers.map(v => v.price)) }}</text>
          <text class="btn-desc" v-else>暂无可用券</text>
        </view>
        <text class="btn-arrow">></text>
      </view>

      <!-- 警告提示 -->
      <view class="warning-notice">
        <view class="warning-icon">⚠</view>
        <text class="warning-text">请以最终质检回收价格为准，如质检金额大于等于询价金额，将自动确认交易并打款</text>
      </view>
      </view>
    </view>

    <view class="content-area">
    <!-- 三步换钱 -->
    <view class="process-section">
      <text class="section-title">三步换钱</text>
      <view class="process-steps">
        <view class="step-item">
          <view class="step-icon">📦</view>
          <text class="step-text">{{ selectedDelivery === 'pickup' ? '包邮寄出' : '自行寄出' }}</text>
        </view>
        <view class="step-arrow">></view>
        <view class="step-item">
          <view class="step-icon">📋</view>
          <text class="step-text">专业质检估价</text>
        </view>
        <view class="step-arrow">></view>
        <view class="step-item">
          <view class="step-icon">💰</view>
          <text class="step-text">确认回收秒到账</text>
        </view>
      </view>
    </view>

    <!-- 配送方式 -->
    <view class="delivery-section">
      <view class="delivery-options">
        <view
          class="delivery-option"
          :class="{ active: selectedDelivery === 'pickup' }"
          @click="selectDelivery('pickup')"
        >
          <view class="option-content">
            <text class="option-name">快递上门</text>
            <view class="option-tag">
              <text class="tag-text">免费</text>
            </view>
          </view>
        </view>
        <view
          class="delivery-option"
          :class="{ active: selectedDelivery === 'self' }"
          @click="selectDelivery('self')"
        >
          <text class="option-name">自行寄出</text>
        </view>
      </view>

      <!-- 快递上门内容 -->
      <view v-if="selectedDelivery === 'pickup'" class="pickup-content">
        <!-- 地址选择 -->
        <view class="address-item" @click="selectAddress">
          <view class="address-icon">
            <u-icon name="map" color="#333" size="18"></u-icon>
          </view>
          <view class="address-content">
            <text class="address-text" v-if="!selectedAddress">请选择取件地址</text>
            <view v-else class="selected-address">
              <text class="address-name">{{ selectedAddress.name }} {{ selectedAddress.mobile }}</text>
              <text class="address-detail">{{ selectedAddress.full_address }}</text>
            </view>
          </view>
          <view class="divider-line"></view>
          <text class="address-action">地址簿</text>
        </view>

        <!-- 预约时间 -->
        <view class="time-item" :class="{ disabled: !selectedAddress }" @click="showTimeModal">
          <view class="time-icon">
            <svg viewBox="0 0 1024 1024" width="32" height="32">
              <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m176.5 585.7l-28.6 39c-4.4 6.1-11.9 9.3-19.4 9.3-4.7 0-9.5-1.4-13.6-4.3L512 609.7l-114.9 84c-9.9 7.2-23.8 5.1-31-4.9l-28.6-39c-7.2-9.9-5.1-23.8 4.9-31L457 515.3V264c0-11 9-20 20-20h70c11 0 20 9 20 20v251.3l114.6 103.5c9.9 7.1 12 21 4.9 30.9z" :fill="selectedAddress ? '#333' : '#ccc'"/>
            </svg>
          </view>
          <text class="time-text">期望上门时间</text>
          <text class="time-action" :class="{ disabled: !selectedAddress }">
            {{ !selectedAddress ? '请先选择地址' : (selectedTime || '尽快上门') }} >
          </text>
        </view>
      </view>

      <!-- 自行寄出内容 -->
      <view v-if="selectedDelivery === 'self'" class="self-content">
        <!-- 收货地址 -->
        <view class="address-item">
          <view class="address-icon orange-bg">
            <text class="address-text-icon">收</text>
          </view>
          <view class="address-info">
            <text class="address-name">放心星仓库 13060000687</text>
            <text class="address-detail">四川省成都市龙泉驿区青羊镇街道龙井3组远洋快递</text>
          </view>
          <text class="copy-btn">复制</text>
        </view>

        <!-- 快递公司 -->
        <view class="express-item" @click="showExpressModal">
          <view class="express-icon">
            <u-icon name="car" color="#333" size="32"></u-icon>
          </view>
          <text class="express-text">快递公司</text>
          <text class="express-action">{{ selectedExpress || '请选择快递公司' }} ></text>
        </view>

        <!-- 快递单号 -->
        <view class="tracking-item">
          <view class="tracking-icon">
            <u-icon name="order" color="#333" size="32"></u-icon>
          </view>
          <text class="tracking-text">快递单号</text>
          <input
            class="tracking-input"
            v-model="trackingNumber"
            placeholder="请输入快递单号"
            maxlength="30"
          />
        </view>
      </view>
    </view>
    </view>

    <!-- 底部操作 -->
    <view class="bottom-action">
      <view class="cart-section">
        <view class="cart-icon" @click="goToCart">
          <view class="cart-badge">{{ cartCount }}</view>
          <svg viewBox="0 0 1024 1024" width="24" height="24">
            <path d="M922.9 701.9H327.4l29.9-60.9 496.8-.9c16.8 0 31.2-12.1 34.2-28.6l68.8-385.1c1.8-10.1-.9-20.5-7.5-28.4a34.99 34.99 0 0 0-26.6-12.5l-632-2.1-5.4-25.4c-3.4-16.2-17.4-28-34.6-28H96.5a35.3 35.3 0 1 0 0 70.6h125.9L246 312.8l58.1 281.3-74.8 122.1a34.96 34.96 0 0 0-3 36.8c6.1 11.9 18.4 19.4 31.5 19.4h62.8a102.43 102.43 0 0 0-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7h161.1a102.43 102.43 0 0 0-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7H923c19.4 0 35.3-15.8 35.3-35.3a35.42 35.42 0 0 0-35.4-35.2zM305.7 253l575.8 1.9-56.4 315.8-452.3.8L305.7 253zm96.9 612.7c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 0 1-31.6 31.6zm325.1 0c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 0 1-31.6 31.6z" fill="currentColor"/>
          </svg>
          <text class="cart-text">稍后发货</text>
        </view>
      </view>
      <view class="submit-button" :class="{ submitting: submitting }" @click="submitOrder">
        <text class="submit-text" v-if="!submitting">确认回收</text>
        <text class="submit-text" v-else>提交中...</text>
      </view>
    </view>

    <!-- 加价券使用提示弹窗 -->
    <view class="voucher-tip-modal" v-if="showVoucherTip" @click="closeVoucherTip">
      <view class="tip-modal-content" @click.stop>
        <view class="tip-content">
          <view class="tip-icon">
            <text class="icon-text">{{ voucherTipIcon }}</text>
          </view>
          <text class="tip-title">{{ voucherTipTitle }}</text>
          <text class="tip-message">{{ voucherTipMessage }}</text>
        </view>
        <view class="tip-actions">
          <view class="tip-btn" @click="closeVoucherTip">
            <text class="btn-text">知道了</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加价券弹窗 -->
    <view class="coupon-modal" v-if="showCoupon" @click="hideCouponModal">
      <view class="coupon-modal-content" @click.stop>
        <view class="coupon-header">
          <text class="coupon-title">我的加价券</text>
          <view class="coupon-close-btn" @click="hideCouponModal">
            <svg viewBox="0 0 1024 1024" width="24" height="24">
              <path d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3.1-3.6-7.6-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 0 0 203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3.1 3.6 7.6 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z" fill="currentColor"/>
            </svg>
          </view>
        </view>

        <!-- 加价券列表 -->
        <view class="coupon-container">
          <!-- 加载状态 -->
          <view v-if="voucherLoading" class="loading-state">
            <text class="loading-text">加载中...</text>
          </view>

          <view v-else-if="couponList.length > 0" class="coupon-list">
            <view
              class="coupon-card"
              v-for="(coupon, index) in couponList"
              :key="coupon.id"
              :class="{
                selected: selectedCoupon === index,
                disabled: !canUseVoucher(coupon, currentPrice)
              }"
              @click="canUseVoucher(coupon, currentPrice) ? selectCoupon(index) : null"
            >
              <view class="coupon-left-section">
                <view class="coupon-amount">¥{{ coupon.price }}</view>
                <view class="coupon-condition">满{{ coupon.min_condition_money }}可用</view>
              </view>
              <view class="coupon-right-section">
                <view class="coupon-name">{{ coupon.title }}</view>
                <view class="coupon-desc">{{ coupon.type_name }}</view>
                <view v-if="canUseVoucher(coupon, currentPrice)" class="coupon-benefit">
                  <text class="benefit-text">使用后可得：¥{{ currentPrice + coupon.price }}</text>
                </view>
                <view class="coupon-time">有效期至 {{ coupon.expire_time_text || coupon.expire_time }}</view>
                <view v-if="!canUseVoucher(coupon, currentPrice)" class="coupon-disabled-reason">
                  <text v-if="currentPrice < coupon.min_condition_money">
                    需满{{ coupon.min_condition_money }}元
                  </text>
                  <text v-else>不可用</text>
                </view>
              </view>
              <view class="coupon-select-icon" v-if="selectedCoupon === index">
                <svg viewBox="0 0 1024 1024" width="20" height="20">
                  <path d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z" fill="currentColor"/>
                </svg>
              </view>
            </view>
          </view>

          <!-- 无加价券状态 -->
          <view v-else class="empty-coupon">
            <view class="empty-icon">
              <svg viewBox="0 0 1024 1024" width="80" height="80">
                <path d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zM224 896V128h576v768H224z m128-640h320v64H352v-64z m0 128h320v64H352v-64z m0 128h320v64H352v-64z m0 128h160v64H352v-64z" fill="#ddd"/>
              </svg>
            </view>
            <text class="empty-text">暂无加价券</text>
            <text class="empty-desc">完成更多回收任务可获得加价券奖励</text>
          </view>
        </view>

        <!-- 底部按钮 -->
        <view class="coupon-footer">
          <view v-if="selectedCoupon >= 0" class="selected-info">
            <text class="selected-text">已选择：{{ couponList[selectedCoupon]?.title }}</text>
            <text class="selected-benefit">+¥{{ couponList[selectedCoupon]?.price }}</text>
          </view>
          <view class="coupon-confirm-btn" @click="confirmCoupon">
            <text class="coupon-confirm-text">
              {{ selectedCoupon >= 0 ? '确认使用' : (couponList.length > 0 ? '不使用优惠券' : '我知道了') }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 快递公司选择弹窗 -->
    <view class="express-modal" v-if="showExpressSelect" @click="hideExpressModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">选择快递公司</text>
          <view class="close-btn" @click="hideExpressModal">×</view>
        </view>

        <view class="express-list">
          <view
            class="express-option"
            v-for="express in expressList"
            :key="express"
            @click="selectExpress(express)"
          >
            <text class="express-name">{{ express }}</text>
            <view class="express-check" v-if="selectedExpress === express">✓</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 右侧固定回收标准按钮 -->
    <view class="fixed-standard-btn" @click="showStandardModal">
      <text class="fixed-standard-text">回</text>
      <text class="fixed-standard-text">收</text>
      <text class="fixed-standard-text">标</text>
      <text class="fixed-standard-text">准</text>
    </view>



    <!-- 回收标准弹窗 -->
    <view class="standard-modal" v-if="showStandard" @click="hideStandardModal">
      <view class="standard-modal-content" @click.stop>
        <view class="standard-header">
          <text class="standard-title">回收标准</text>
          <view class="close-btn" @click="hideStandardModal">×</view>
        </view>

        <text class="standard-subtitle">以下情况请使用拍照估价进行精准价回收</text>

        <!-- 加载状态 -->
        <view v-if="standardsLoading" class="standards-loading">
          <text class="loading-text">正在加载回收标准...</text>
        </view>

        <!-- 回收标准列表 -->
        <view v-else-if="recycleStandards.length > 0" class="standard-grid">
          <view class="standard-item" v-for="item in recycleStandards" :key="item.id">
            <view class="standard-image">
              <image
                v-if="item.image && !item.imageError"
                :src="getStandardImageUrl(item)"
                mode="aspectFill"
                class="standard-img"
                @error="() => handleImageError(item)"
                @load="() => handleImageLoad(item)"
              />
              <view v-else class="standard-placeholder">
                <text class="placeholder-text">{{ item.image ? '图片加载失败' : '暂无图片' }}</text>
              </view>
            </view>
            <text class="standard-label">{{ item.title }}</text>
          </view>
        </view>

        <!-- 无数据状态 -->
        <view v-else class="no-standards">
          <text class="no-standards-text">该分类暂无回收标准</text>
        </view>

        <view class="standard-buttons">
          <view class="photo-estimate-btn" @click="goToPhotoEstimate">
            <text class="photo-estimate-text">拍照估价</text>
          </view>
          <view class="standard-confirm-btn" @click="confirmStandard">
            <text class="confirm-text">我知道了</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 时间选择弹窗 -->
    <view class="time-modal" v-if="showTime" @click="hideTimeModal">
      <view class="time-modal-content" @click.stop>
        <view class="time-header">
          <text class="time-title">期望上门时间</text>
          <view class="close-btn" @click="hideTimeModal">×</view>
        </view>

        <!-- 左右分栏布局 -->
        <view class="time-container">
          <!-- 左侧日期选择 -->
          <view class="date-sidebar">
            <view
              class="date-item"
              :class="{ active: selectedDate === 'today' }"
              @click="selectDate('today')"
            >
              <text class="date-name">今天</text>
              <text class="date-desc">{{ todayDesc }}</text>
            </view>
            <view
              class="date-item"
              :class="{ active: selectedDate === 'tomorrow' }"
              @click="selectDate('tomorrow')"
            >
              <text class="date-name">明天</text>
              <text class="date-desc">{{ tomorrowDesc }}</text>
            </view>
            <view
              class="date-item"
              :class="{ active: selectedDate === 'dayafter' }"
              @click="selectDate('dayafter')"
            >
              <text class="date-name">后天</text>
              <text class="date-desc">{{ dayafterDesc }}</text>
            </view>
          </view>

          <!-- 右侧时间选择 -->
          <view class="time-content">
            <!-- 今天的时间选项 -->
            <view v-if="selectedDate === 'today'" class="time-slots">
              <view
                class="time-slot urgent"
                :class="{ active: selectedTimeSlot === 'today-urgent' }"
                @click="selectTimeSlot('today-urgent')"
              >
                <text class="slot-title">尽快上门</text>
                <text class="slot-desc">工作日2小时内</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'today-afternoon' }"
                @click="selectTimeSlot('today-afternoon')"
              >
                <text class="slot-title">14:00-16:00</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'today-evening' }"
                @click="selectTimeSlot('today-evening')"
              >
                <text class="slot-title">16:00-18:00</text>
              </view>
            </view>

            <!-- 明天的时间选项 -->
            <view v-if="selectedDate === 'tomorrow'" class="time-slots">
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'tomorrow-morning' }"
                @click="selectTimeSlot('tomorrow-morning')"
              >
                <text class="slot-title">09:00-11:00</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'tomorrow-noon' }"
                @click="selectTimeSlot('tomorrow-noon')"
              >
                <text class="slot-title">11:00-13:00</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'tomorrow-afternoon' }"
                @click="selectTimeSlot('tomorrow-afternoon')"
              >
                <text class="slot-title">14:00-16:00</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'tomorrow-evening' }"
                @click="selectTimeSlot('tomorrow-evening')"
              >
                <text class="slot-title">16:00-18:00</text>
              </view>
            </view>

            <!-- 后天的时间选项 -->
            <view v-if="selectedDate === 'dayafter'" class="time-slots">
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'dayafter-morning' }"
                @click="selectTimeSlot('dayafter-morning')"
              >
                <text class="slot-title">09:00-11:00</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'dayafter-noon' }"
                @click="selectTimeSlot('dayafter-noon')"
              >
                <text class="slot-title">11:00-13:00</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'dayafter-afternoon' }"
                @click="selectTimeSlot('dayafter-afternoon')"
              >
                <text class="slot-title">14:00-16:00</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'dayafter-evening' }"
                @click="selectTimeSlot('dayafter-evening')"
              >
                <text class="slot-title">16:00-18:00</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 成色说明弹窗 -->
    <view class="condition-modal" v-if="showCondition" @click="hideConditionModal">
      <view class="condition-modal-content" @click.stop>
        <view class="condition-header">
          <text class="condition-title">成色说明</text>
          <view class="close-btn" @click="hideConditionModal">×</view>
        </view>

        <view class="condition-content">
          <view class="condition-item">
            <view class="condition-item-header">
              <view class="condition-badge new">未使用</view>
            </view>
            <view class="condition-item-body">
              <view class="condition-description">
                <text class="condition-text">整体品相极佳，仅存在放置痕迹且无严重做工瑕疵</text>
              </view>
              <view class="condition-images">
                <image class="condition-img" :src="img('addon/yz_she/cs/1.png')" mode="aspectFill" />
              </view>
            </view>
          </view>

          <view class="condition-item">
            <view class="condition-item-header">
              <view class="condition-badge light">轻微使用</view>
            </view>
            <view class="condition-item-body">
              <view class="condition-description">
                <text class="condition-text">整体品相佳，仅试穿或缺少重要零部件(吊牌等)、做工瑕疵少</text>
              </view>
              <view class="condition-images">
                <image class="condition-img" :src="img('addon/yz_she/cs/2.png')" mode="aspectFill" />
              </view>
            </view>
          </view>

          <view class="condition-item">
            <view class="condition-item-header">
              <view class="condition-badge obvious">明显使用</view>
            </view>
            <view class="condition-item-body">
              <view class="condition-description">
                <text class="condition-text">存在轻微磨损/氧化/褶皱等瑕疵或水洗/护理，但无严重瑕疵(如严重破损/变形/氧化等)</text>
              </view>
              <view class="condition-images">
                <image class="condition-img" :src="img('addon/yz_she/cs/3.png')" mode="aspectFill" />
              </view>
            </view>
          </view>
        </view>


      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, onUnmounted } from 'vue'
import { onShow, onLoad, onUnload } from '@dcloudio/uni-app'
import { getMyVoucherList, calculateVoucherDiscount } from '@/addon/yz_she/api/voucher'
import { getGoodsDetail } from '@/addon/yz_she/api/brand'
import { getRecycleStandardsByCategory } from '@/addon/yz_she/api/recycle_standard'
import { createRecycleOrder } from '@/addon/yz_she/api/recycle_order'
import { getCartCount, addToCart } from '@/addon/yz_she/api/quote'
import { img } from '@/utils/common'

// 响应式数据
const productInfo = ref({
  id: '',
  name: '',
  code: '',
  image: '',
  category_id: '',
  brand: null // 品牌信息
})

// 价格数据
const priceData = ref({
  new: 0,      // 全新未穿着价格
  light: 0,    // 轻微穿着价格
  obvious: 0   // 明显穿着价格
})

// 加载状态
const productLoading = ref(false)

// 页面参数
const pageParams = ref({})
// 保存当前商品ID，防止页面切换时丢失
const currentProductId = ref('')

const selectedPriceType = ref('new')
const cartCount = ref(0)
const showCoupon = ref(false)
const selectedCoupon = ref(-1) // 选中的加价券索引，-1表示未选中
const selectedVoucherInfo = ref(null) // 选中的加价券信息

// 加价券数据
const couponList = ref([])
const voucherLoading = ref(false)
const selectedDelivery = ref('pickup') // pickup: 快递上门, self: 自行寄出
const showExpressSelect = ref(false) // 控制快递公司选择弹窗
const showStandard = ref(false) // 控制回收标准弹窗
const selectedExpress = ref('') // 选中的快递公司
const trackingNumber = ref('') // 快递单号
const showTrackingModal = ref(false) // 控制快递单号输入弹窗
const selectedAddress = ref(null) // 选中的地址
const showTime = ref(false) // 控制时间选择弹窗
const selectedTime = ref('尽快上门') // 选中的时间
const selectedDate = ref('today') // 选中的日期
const selectedTimeSlot = ref('today-urgent') // 选中的时间段

// 日期描述
const todayDesc = ref('')
const tomorrowDesc = ref('')
const dayafterDesc = ref('')

// 快递公司列表
const expressList = [
  '顺丰速运',
  '京东物流',
  '德邦快递',
  '中通快递',
  '韵达速递',
  '圆通速递',
  '申通快递',
  '极兔速递'
]

// 时间选项映射
const timeSlotMap = {
  'today-urgent': '尽快上门',
  'today-afternoon': '今天 14:00-16:00',
  'today-evening': '今天 16:00-18:00',
  'tomorrow-morning': '明天 09:00-11:00',
  'tomorrow-noon': '明天 11:00-13:00',
  'tomorrow-afternoon': '明天 14:00-16:00',
  'tomorrow-evening': '明天 16:00-18:00',
  'dayafter-morning': '后天 09:00-11:00',
  'dayafter-noon': '后天 11:00-13:00',
  'dayafter-afternoon': '后天 14:00-16:00',
  'dayafter-evening': '后天 16:00-18:00'
}

// 回收标准数据
const recycleStandards = ref([])
const standardsLoading = ref(false)

// 成色说明弹窗
const showCondition = ref(false) // 控制成色说明弹窗

// 订单提交相关
const submitting = ref(false)
const quoteOrderId = ref(null) // 估价订单ID（如果是从估价转回收）

// 计算属性
// 当前选中的价格
const currentPrice = computed(() => {
  return priceData.value[selectedPriceType.value] || 0
})

// 是否有可用的加价券
const hasCoupon = computed(() => {
  return Array.isArray(couponList.value) && couponList.value.some(coupon => canUseVoucher(coupon, currentPrice.value))
})

// 当前可用的加价券列表
const availableVouchers = computed(() => {
  return Array.isArray(couponList.value) ? couponList.value.filter(coupon => canUseVoucher(coupon, currentPrice.value)) : []
})

// 加价券优惠金额
const voucherDiscount = computed(() => {
  if (selectedVoucherInfo.value && selectedVoucherInfo.value.can_use) {
    return selectedVoucherInfo.value.discount_amount || 0
  }
  return 0
})

// 选中加价券后的最终价格
const finalPrice = computed(() => {
  const basePrice = currentPrice.value || 0
  const discount = voucherDiscount.value || 0
  return basePrice + discount
})

// 处理后的商品图片路径
const productImageSrc = computed(() => {
  if (productInfo.value.image) {
    return img(productInfo.value.image)
  }
  return ''
})

// 方法
const selectPriceType = (type: string) => {
  selectedPriceType.value = type

  // 重新计算当前选中的加价券
  if (selectedCoupon.value >= 0) {
    calculateSelectedVoucher()
  } else {
    // 如果没有选中加价券，自动选择最优的可用加价券
    autoSelectBestVoucher()
  }
}

// 自动选择最优的可用加价券
const autoSelectBestVoucher = () => {
  if (!Array.isArray(couponList.value) || couponList.value.length === 0) {
    return
  }

  // 找到可用的加价券中金额最大的
  const availableVouchers = couponList.value.filter(voucher =>
    canUseVoucher(voucher, currentPrice.value)
  )

  if (availableVouchers.length > 0) {
    // 按加价金额降序排序，选择最优的
    const bestVoucher = availableVouchers.sort((a, b) => b.price - a.price)[0]
    const bestIndex = couponList.value.findIndex(voucher => voucher.id === bestVoucher.id)

    if (bestIndex >= 0) {
      selectedCoupon.value = bestIndex
      calculateSelectedVoucher()
    }
  }
}

// 检查特定价格是否有可用的优惠券
const hasVoucherForPrice = (price: number) => {
  if (!Array.isArray(couponList.value) || couponList.value.length === 0 || !price) {
    console.log('hasVoucherForPrice返回false:', {
      isArray: Array.isArray(couponList.value),
      length: couponList.value?.length,
      price
    })
    return false
  }

  const result = couponList.value.some(voucher => canUseVoucher(voucher, price))
  console.log(`hasVoucherForPrice(${price}):`, result, '可用券:', couponList.value.filter(v => canUseVoucher(v, price)))
  return result
}

// 加载商品详情
const loadProductInfo = async (productId: string) => {
  if (!productId) {
    return
  }

  try {
    productLoading.value = true
    const res = await getGoodsDetail(parseInt(productId))
    const goodsData = res.data



    // 检查数据是否存在
    if (!goodsData) {
      throw new Error('商品数据为空')
    }

    // 更新商品信息（包含品牌信息）
    productInfo.value = {
      id: goodsData.id,
      name: goodsData.name || '商品名称',
      code: goodsData.code || '',
      image: goodsData.image || '',
      category_id: goodsData.category_id || '',
      brand: goodsData.brand || null // 保存品牌信息
    }

    console.log('商品信息已更新:', productInfo.value)
    console.log('品牌信息:', goodsData.brand)

    // 更新价格数据
    priceData.value = {
      new: parseFloat(goodsData.price_new || 0),
      light: parseFloat(goodsData.price_used || 0),
      obvious: parseFloat(goodsData.price_damaged || 0)
    }
    console.log('商品信息加载完成，价格数据:', priceData.value)
    console.log('当前选中价格:', currentPrice.value)

    // 商品信息加载完成后，加载优惠券列表
    await loadVoucherList()





  } catch (error) {
    console.error('加载商品信息失败:', error)

    // 商品信息加载失败，显示错误信息
    uni.showToast({
      title: '商品信息加载失败',
      icon: 'error',
      duration: 2000
    })

    uni.showToast({
      title: 'API调用失败，使用测试数据',
      icon: 'none',
      duration: 2000
    })
  } finally {
    productLoading.value = false
  }
}

// 获取用户加价券列表
const loadVoucherList = async () => {
  try {
    voucherLoading.value = true

    // 获取用户加价券列表
    const res = await getMyVoucherList({
      status: 1, // 只获取未使用的券
      product_price: currentPrice.value,
      product_id: productInfo.value.id
    })
    if (res.code === 1) {
      couponList.value = res.data.data || []

      console.log('加载的优惠券列表:', couponList.value)
      console.log('当前价格:', currentPrice.value)
      console.log('可用优惠券数量:', couponList.value.filter(c => canUseVoucher(c, currentPrice.value)).length)

      // 自动选择最优的可用加价券
      if (selectedCoupon.value < 0) {
        autoSelectBestVoucher()
      }
    }

  } catch (error) {
    console.error('获取加价券列表失败:', error)
    couponList.value = []
  } finally {
    voucherLoading.value = false
  }
}

// 判断加价券是否可用
const canUseVoucher = (voucher: any, price: number) => {
  // 检查状态
  if (voucher.status !== 1) return false

  // 检查是否过期
  const now = Math.floor(Date.now() / 1000) // 转换为秒级时间戳
  const expireTime = parseInt(voucher.expire_time) // API返回的是秒级时间戳
  if (now > expireTime) return false

  // 检查使用条件
  if (price < voucher.min_condition_money) return false

  return true
}

// 计算选中的加价券优惠
const calculateSelectedVoucher = async () => {
  if (selectedCoupon.value < 0 || !couponList.value[selectedCoupon.value]) {
    selectedVoucherInfo.value = null
    return
  }

  const voucher = couponList.value[selectedCoupon.value]

  // 计算逻辑
  const canUse = canUseVoucher(voucher, currentPrice.value)
  if (canUse) {
    selectedVoucherInfo.value = {
      can_use: true,
      discount_amount: voucher.price,
      final_price: currentPrice.value + voucher.price,
      voucher_info: voucher,
      message: '可以使用'
    }
  } else {
    selectedVoucherInfo.value = {
      can_use: false,
      discount_amount: 0,
      final_price: currentPrice.value,
      message: '不满足使用条件'
    }
  }

  // 正式环境使用API
  // try {
  //   const res = await calculateVoucherDiscount({
  //     voucher_id: voucher.id,
  //     product_price: currentPrice.value,
  //     product_id: productInfo.value.id
  //   })
  //   selectedVoucherInfo.value = res.data
  // } catch (error) {
  //   console.error('计算加价券优惠失败:', error)
  //   selectedVoucherInfo.value = null
  // }
}

// 获取回收标准数据
const getRecycleStandards = async () => {
  console.log('开始获取回收标准，当前商品信息:', productInfo.value)

  if (!productInfo.value.category_id) {
    console.log('没有分类ID，无法获取回收标准。商品信息:', productInfo.value)

    // 如果没有category_id，尝试重新加载商品信息
    if (productInfo.value.id) {
      console.log('尝试重新加载商品信息以获取分类ID...')
      await loadProductInfo(productInfo.value.id)

      // 重新检查category_id
      if (!productInfo.value.category_id) {
        console.log('重新加载后仍然没有分类ID')
        uni.showToast({
          title: '商品分类信息缺失',
          icon: 'none'
        })
        return
      }
    } else {
      console.log('商品ID也为空，无法获取商品信息')
      uni.showToast({
        title: '商品信息缺失',
        icon: 'none'
      })
      return
    }
  }

  standardsLoading.value = true
  try {
    console.log(`正在获取分类ID ${productInfo.value.category_id} 的回收标准...`)

    const response = await getRecycleStandardsByCategory(productInfo.value.category_id)

    console.log('回收标准API响应:', response)

    // 根据实际API响应结构处理数据
    if (response && response.data && Array.isArray(response.data)) {
      recycleStandards.value = response.data
      console.log('获取到的回收标准:', recycleStandards.value)

      console.log('获取到回收标准数据，共', recycleStandards.value.length, '项')

      if (recycleStandards.value.length === 0) {
        console.log('该分类暂无回收标准')
      }
    } else if (response && Array.isArray(response)) {
      // 兼容直接返回数组的情况
      recycleStandards.value = response
      console.log('获取到的回收标准:', recycleStandards.value)
    } else {
      console.error('获取回收标准失败: 数据格式错误', response)
      recycleStandards.value = []
      uni.showToast({
        title: '数据格式错误',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取回收标准异常:', error)
    recycleStandards.value = []
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  }
  standardsLoading.value = false
}

// 回收标准相关方法
const showStandardModal = async () => {
  console.log('点击显示回收标准弹窗')

  // 确保有商品信息
  if (!productInfo.value.id) {
    console.log('没有商品信息，无法显示回收标准')
    uni.showToast({
      title: '请先选择商品',
      icon: 'none'
    })
    return
  }

  // 先显示弹窗，再获取数据（提升用户体验）
  showStandard.value = true

  // 获取回收标准数据
  await getRecycleStandards()
}

const hideStandardModal = () => {
  showStandard.value = false
}

const confirmStandard = () => {
  showStandard.value = false
  console.log('确认回收标准')
}

// 成色说明相关方法
const showConditionModal = () => {
  showCondition.value = true
}

const hideConditionModal = () => {
  showCondition.value = false
}

// 跳转到拍照估价页面
const goToPhotoEstimate = () => {
  console.log('从detail页面跳转到拍照估价')
  console.log('当前商品信息:', productInfo.value)

  // 检查是否有商品信息
  if (!productInfo.value.id) {
    uni.showToast({
      title: '商品信息缺失',
      icon: 'none'
    })
    return
  }

  // 从商品详情中获取品牌信息
  let brandInfo = null

  // 如果商品信息中有brand字段，直接使用
  if (productInfo.value.brand) {
    brandInfo = productInfo.value.brand
  } 

  console.log('使用品牌信息:', brandInfo)

  // 将数据存储到全局存储中，避免URL参数长度限制
  const jumpData = {
    from: 'detail',
    productId: productInfo.value.id,
    productName: productInfo.value.name || '',
    productImage: productInfo.value.image || '',
    categoryId: productInfo.value.category_id || '',
    brandId: brandInfo.id,
    brandName: brandInfo.name || '',
    brandLogo: brandInfo.logo || ''
  }

  console.log('存储跳转数据:', jumpData)

  // 存储到本地缓存
  uni.setStorageSync('photoEvaluateData', jumpData)

  // 简单跳转，不携带复杂参数
  uni.navigateTo({
    url: '/addon/yz_she/pages/evaluate/photo?from=detail'
  })
}

// 处理回收标准图片URL（与商品图片保持一致）
const getStandardImageUrl = (item) => {
  if (!item || !item.image) return ''

  // 直接使用img函数处理，与商品图片保持一致
  return img(item.image)
}

// 图片加载事件处理
const handleImageLoad = (item) => {
  // 确保图片加载成功时清除错误状态
  if (item.imageError) {
    item.imageError = false
  }
}

const handleImageError = (item) => {
  console.error('回收标准图片加载失败:', item.title)

  // 标记该图片加载失败
  item.imageError = true
}

// 调试函数
const debugProductInfo = () => {
  console.log('=== 调试商品信息 ===')
  console.log('productInfo:', productInfo.value)
  console.log('currentProductId:', currentProductId.value)
  console.log('pageParams:', pageParams.value)

  uni.showModal({
    title: '调试信息',
    content: `商品ID: ${productInfo.value.id}\n分类ID: ${productInfo.value.category_id}\n商品名称: ${productInfo.value.name}`,
    showCancel: false
  })
}

// 地址选择相关方法
const selectAddress = () => {
  // 设置回调信息，包含商品ID和完整的返回URL
  const backUrl = currentProductId.value
    ? `/addon/yz_she/pages/evaluate/detail?id=${currentProductId.value}`
    : '/addon/yz_she/pages/evaluate/detail'

  uni.setStorage({
    key: 'selectAddressCallback',
    data: {
      back: backUrl,
      productId: currentProductId.value
    }
  })

  uni.navigateTo({
    url: '/addon/yz_she/pages/address/index'
  })
}

// 生成日期描述
const generateDateDescriptions = () => {
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(today.getDate() + 1)
  const dayafter = new Date(today)
  dayafter.setDate(today.getDate() + 2)

  todayDesc.value = `${today.getMonth() + 1}月${today.getDate()}日`
  tomorrowDesc.value = `${tomorrow.getMonth() + 1}月${tomorrow.getDate()}日`
  dayafterDesc.value = `${dayafter.getMonth() + 1}月${dayafter.getDate()}日`
}

// 时间选择相关方法
const showTimeModal = () => {
  // 检查是否已选择地址
  if (!selectedAddress.value) {
    uni.showToast({
      title: '请先选择取件地址',
      icon: 'none',
      duration: 2000
    })
    return
  }

  showTime.value = true
}

const hideTimeModal = () => {
  showTime.value = false
}

const selectDate = (date: string) => {
  selectedDate.value = date
  // 不自动选择时间段，让用户手动选择
}

const selectTimeSlot = (slot: string) => {
  selectedTimeSlot.value = slot
  // 直接确认选择，不需要额外的确认按钮
  const timeText = timeSlotMap[slot] || slot
  selectedTime.value = timeText
  showTime.value = false
  console.log('选择时间:', timeText)
}

const selectDelivery = (type: string) => {
  selectedDelivery.value = type
  console.log('选择配送方式:', type)
}

const goToCart = async () => {
  // 如果没有选择商品成色，先添加到购物车
  if (!selectedPriceType.value || !currentPrice.value) {
    uni.showToast({
      title: '请先选择商品成色',
      icon: 'none'
    })
    return
  }

  try {
    // 构建添加到购物车的数据
    const cartData = {
      category_id: productInfo.value.category_id || 0,
      brand_id: productInfo.value.brand?.id || 0,
      product_id: productInfo.value.id || 0,
      product_name: productInfo.value.name,
      product_code: productInfo.value.code,
      product_image: productInfo.value.image,
      quote_price: parseFloat(finalPrice.value),
      condition_type: selectedPriceType.value,
      voucher_id: selectedVoucherInfo.value?.id || 0,
      voucher_amount: parseFloat(voucherDiscount.value) || 0,
      note: `成色：${getPriceTypeText(selectedPriceType.value)}`
    }

    const result = await addToCart(cartData)
    if (result.code === 1) {
      uni.showToast({
        title: '已添加到购物车',
        icon: 'success'
      })

      // 更新购物车数量
      await loadCartCount()

      // 延迟跳转到购物车页面
      setTimeout(() => {
        uni.navigateTo({
          url: '/addon/yz_she/pages/order/quote-list?status=3' // 跳转到待发货状态
        })
      }, 1500)
    } else {
      throw new Error(result.msg || '添加失败')
    }
  } catch (error) {
    console.error('添加到购物车失败:', error)
    uni.showToast({
      title: error.message || '添加失败',
      icon: 'none'
    })
  }
}

// 加载购物车数量
const loadCartCount = async () => {
  try {
    const res = await getCartCount()
    if (res.code === 1) {
      cartCount.value = res.data.count || 0
    }
  } catch (error) {
    console.error('获取购物车数量失败:', error)
    cartCount.value = 0
  }
}

// 获取成色文本
const getPriceTypeText = (type: string) => {
  switch (type) {
    case 'new':
      return '全新未穿着'
    case 'light':
      return '轻微穿着'
    case 'obvious':
      return '明显穿着'
    default:
      return '未知成色'
  }
}

// 加价券提示弹窗相关
const showVoucherTip = ref(false)
const voucherTipIcon = ref('🎫')
const voucherTipTitle = ref('')
const voucherTipMessage = ref('')

// 显示加价券使用提示
const showVoucherUseTip = (isUsing) => {
  if (isUsing) {
    voucherTipIcon.value = '✓'
    voucherTipTitle.value = '已使用加价券'
    voucherTipMessage.value = '恭喜！您已成功使用加价券，回收价格已提升。请注意加价券有效期，及时完成交易。'
  } else {
    voucherTipIcon.value = '!'
    voucherTipTitle.value = '未使用加价券'
    voucherTipMessage.value = '您当前未使用加价券，可以点击"选择加价券"来提升回收价格，获得更多收益。'
  }
  showVoucherTip.value = true
}

// 关闭提示弹窗
const closeVoucherTip = () => {
  showVoucherTip.value = false
}

const showCouponModal = async () => {
  // 加载加价券列表
  await loadVoucherList()
  showCoupon.value = true
}

const hideCouponModal = () => {
  showCoupon.value = false
}

const selectCoupon = (index: number) => {
  selectedCoupon.value = selectedCoupon.value === index ? -1 : index
  // 计算选中的加价券优惠
  calculateSelectedVoucher()
}

const confirmCoupon = () => {
  if (couponList.value.length > 0 && selectedCoupon.value >= 0) {
    const selected = couponList.value[selectedCoupon.value]
    // 显示使用加价券提示
    setTimeout(() => {
      showVoucherUseTip(true)
    }, 300)
  } else if (couponList.value.length > 0) {
    // 用户选择不使用优惠券
    selectedCoupon.value = -1
    selectedVoucherInfo.value = null
    // 显示未使用加价券提示
    setTimeout(() => {
      showVoucherUseTip(false)
    }, 300)
  }
  showCoupon.value = false
}

const showExpressModal = () => {
  showExpressSelect.value = true
}

const hideExpressModal = () => {
  showExpressSelect.value = false
}

const selectExpress = (express: string) => {
  selectedExpress.value = express
  showExpressSelect.value = false
  uni.showToast({
    title: `已选择${express}`,
    icon: 'success'
  })
}

// 删除了扫码功能，快递单号直接输入

const submitOrder = async () => {
  console.log('开始提交回收订单')

  // 防止重复提交
  if (submitting.value) {
    return
  }

  try {
    // 验证必填信息
    if (!validateOrderData()) {
      return
    }

    submitting.value = true

    // 构建订单数据
    const orderData = buildOrderData()
    console.log('订单数据:', orderData)

    // 调用API创建订单
    const response = await createRecycleOrder(orderData)

    if (response.code === 1) {
      uni.showToast({
        title: '订单提交成功',
        icon: 'success',
        duration: 2000
      })

      // 延迟跳转到成功页面
      setTimeout(() => {
        uni.redirectTo({
          url: `/addon/yz_she/pages/order/success/detail-success?recycleOrderId=${response.data.id}`
        })
      }, 1000)
    } else {
      throw new Error(response.msg || '订单提交失败')
    }

  } catch (error) {
    console.error('提交订单失败:', error)
    uni.showToast({
      title: error.message || '提交失败，请重试',
      icon: 'none',
      duration: 3000
    })
  } finally {
    submitting.value = false
  }
}

// 验证订单数据
const validateOrderData = () => {
  // 检查商品信息
  if (!productInfo.value.id) {
    uni.showToast({
      title: '商品信息缺失',
      icon: 'none'
    })
    return false
  }

  // 检查价格选择
  if (!currentPrice.value || currentPrice.value <= 0) {
    uni.showToast({
      title: '请选择商品价格',
      icon: 'none'
    })
    return false
  }

  // 如果是快递上门，检查地址和时间
  if (selectedDelivery.value === 'pickup') {
    if (!selectedAddress.value) {
      uni.showToast({
        title: '请选择取件地址',
        icon: 'none'
      })
      return false
    }

    if (!selectedTime.value) {
      uni.showToast({
        title: '请选择上门时间',
        icon: 'none'
      })
      return false
    }
  }

  // 如果是用户自寄，检查快递信息
  if (selectedDelivery.value === 'self') {
    if (!selectedExpress.value) {
      uni.showToast({
        title: '请选择快递公司',
        icon: 'none'
      })
      return false
    }

    if (!trackingNumber.value || !trackingNumber.value.trim()) {
      uni.showToast({
        title: '请输入快递单号',
        icon: 'none'
      })
      return false
    }
  }

  return true
}

// 构建订单数据
const buildOrderData = () => {
  const basePrice = currentPrice.value || 0
  const voucherAmount = voucherDiscount.value || 0
  const finalPriceValue = finalPrice.value || basePrice

  const orderData = {
    // 基础商品信息
    category_id: parseInt(productInfo.value.category_id) || 0,
    brand_id: productInfo.value.brand?.id || null,
    product_id: parseInt(productInfo.value.id) || null,
    product_name: productInfo.value.name || '',
    product_code: productInfo.value.code || '',
    product_image: productInfo.value.image || '',

    // 价格信息
    expected_price: basePrice,
    voucher_amount: voucherAmount,
    final_price: finalPriceValue,
    total_amount: finalPriceValue,
    express_fee: 0, // 免费快递

    // 订单类型
    source_type: 2, // 2=直接回收
    delivery_type: selectedDelivery.value === 'pickup' ? 1 : 2, // 1=快递上门, 2=用户自寄

    // 数量
    quantity: 1,

    // 加价券信息
    voucher_id: selectedVoucherInfo.value?.voucher_info?.id || null,

    // 估价订单关联
    quote_order_id: null
  }

  // 快递上门的信息
  if (selectedDelivery.value === 'pickup' && selectedAddress.value) {
    orderData.pickup_address_id = selectedAddress.value.id
    orderData.pickup_contact_name = selectedAddress.value.name
    orderData.pickup_contact_phone = selectedAddress.value.mobile
    orderData.pickup_address_detail = selectedAddress.value.full_address || selectedAddress.value.address_detail || ''
    orderData.pickup_time = getFormattedPickupTime()

    console.log('快递上门地址信息:', {
      address_id: selectedAddress.value.id,
      contact_name: selectedAddress.value.name,
      contact_phone: selectedAddress.value.mobile,
      address_detail: orderData.pickup_address_detail,
      pickup_time: orderData.pickup_time
    })
  }

  // 用户自寄的信息
  if (selectedDelivery.value === 'self') {
    // 使用默认的收货地址信息
    orderData.pickup_contact_name = '放心星仓库'
    orderData.pickup_contact_phone = '13060000687'
    orderData.admin_note = `用户自寄，快递公司：${selectedExpress.value}，快递单号：${trackingNumber.value}`

    // 添加快递信息字段
    orderData.express_company = selectedExpress.value
    orderData.express_number = trackingNumber.value

    // 自行寄出直接设置为待收货状态
    orderData.status = 2 // 2=待收货

    console.log('自行寄出订单信息:', {
      express_company: orderData.express_company,
      express_number: orderData.express_number,
      status: orderData.status
    })
  }

  // 调试日志
  console.log('构建的订单数据:', {
    pickup_address_detail: orderData.pickup_address_detail,
    voucher_id: orderData.voucher_id,
    selectedVoucherInfo: selectedVoucherInfo.value,
    selectedAddress: selectedAddress.value
  })

  return orderData
}

// 格式化取件时间
const getFormattedPickupTime = () => {
  if (selectedTimeSlot.value && timeSlotMap[selectedTimeSlot.value]) {
    return timeSlotMap[selectedTimeSlot.value]
  }
  return selectedTime.value || '尽快上门'
}

// 监听价格变化，重新加载加价券
watch(currentPrice, () => {
  // 清除当前选中的加价券
  selectedCoupon.value = -1
  selectedVoucherInfo.value = null
  // 如果弹窗是打开的，重新加载券列表
  if (showCoupon.value) {
    loadVoucherList()
  }
})

// 页面加载时获取参数
onLoad((options) => {
  console.log('=== onLoad获取到的参数 ===', options)
  pageParams.value = options || {}
})

onMounted(async () => {
  console.log('=== 开始获取页面参数 ===')

  // 加载购物车数量
  await loadCartCount()

  let productId = ''

  // 方法1: 从URL直接解析
  try {
    const currentUrl = window.location.href
    console.log('当前完整URL:', currentUrl)
    console.log('window.location.search:', window.location.search)
    console.log('window.location.hash:', window.location.hash)

    // 尝试多种方式解析URL参数
    let urlId = null

    // 方式1: 从search参数解析
    if (window.location.search) {
      const urlParams = new URLSearchParams(window.location.search)
      urlId = urlParams.get('id')
      console.log('从search解析的ID:', urlId)
    }

    // 方式2: 从hash参数解析
    if (!urlId && window.location.hash) {
      const hashPart = window.location.hash.split('?')[1]
      if (hashPart) {
        const hashParams = new URLSearchParams(hashPart)
        urlId = hashParams.get('id')
        console.log('从hash解析的ID:', urlId)
      }
    }

    // 方式3: 手动解析URL
    if (!urlId) {
      const url = currentUrl
      const idMatch = url.match(/[?&]id=([^&]+)/)
      if (idMatch) {
        urlId = idMatch[1]
        console.log('手动解析的ID:', urlId)
      }
    }

    if (urlId) {
      productId = urlId
      console.log('从URL直接解析获取到商品ID:', productId)
    }
  } catch (e) {
    console.log('URL直接解析失败:', e)
  }

  // 方法2: 从getCurrentPages获取
  if (!productId) {
    try {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const options = currentPage.options || {}

      console.log('getCurrentPages获取的参数:', options)

      if (options.id) {
        productId = options.id
        console.log('从getCurrentPages获取到商品ID:', productId)
      }
    } catch (e) {
      console.log('getCurrentPages获取失败:', e)
    }
  }

  // 方法3: 从pageParams获取（onLoad设置的）
  if (!productId && pageParams.value.id) {
    productId = pageParams.value.id
    console.log('从pageParams获取到商品ID:', productId)
  }

  // 检查是否有估价订单ID参数
  if (pageParams.value.quote_order_id) {
    quoteOrderId.value = parseInt(pageParams.value.quote_order_id)
    console.log('获取到估价订单ID:', quoteOrderId.value)
  }

  // 方法4: 从本地存储获取（用于页面刷新或地址选择返回）
  if (!productId) {
    const storedProductId = uni.getStorageSync('currentProductId')
    if (storedProductId) {
      productId = storedProductId
      console.log('从本地存储获取到商品ID:', productId)
    }
  }

  // 如果还是没有获取到，显示错误
  if (!productId) {
    uni.showToast({
      title: '缺少商品ID参数',
      icon: 'error',
      duration: 2000
    })
    return
  }

  // 通用URL参数解析
  if (!productId) {
    const currentUrl = window.location.href
    const matches = currentUrl.match(/[?&]id=(\d+)/)
    if (matches && matches[1]) {
      productId = matches[1]
      console.log('通用URL解析获取到商品ID:', productId)
    }
  }

  console.log('=== 最终使用的商品ID:', productId, '===')

  // 保存商品ID到内存和本地存储
  currentProductId.value = productId
  uni.setStorageSync('currentProductId', productId)

  // 加载商品详情
  await loadProductInfo(productId)

  // 生成日期描述
  generateDateDescriptions()



  // 优惠券列表会在商品信息加载完成后自动加载
})

// 页面显示时检查地址回调
onShow(() => {
  // 检查地址选择回调
  const selectAddressCallback = uni.getStorageSync('selectAddressCallback')
  if (selectAddressCallback && selectAddressCallback.address_id) {
    // 恢复商品ID
    if (selectAddressCallback.productId) {
      currentProductId.value = selectAddressCallback.productId

      // 如果商品信息为空，重新加载
      if (!productInfo.value.id) {
        loadProductInfo(selectAddressCallback.productId)
      }
    }

    // 使用回调中的地址信息
    if (selectAddressCallback.address_info) {
      selectedAddress.value = selectAddressCallback.address_info
    } else {
      // 如果没有地址信息，可以根据address_id调用API获取
      // 这里暂时清空选择，让用户重新选择
      selectedAddress.value = null
    }

    // 清除回调数据
    uni.removeStorage({ key: 'selectAddressCallback' })
  }

  // 如果商品信息为空但有保存的商品ID，重新加载商品信息
  if (!productInfo.value.id && currentProductId.value) {
    loadProductInfo(currentProductId.value)
  }
})

// 页面卸载时清理存储（当用户真正离开页面时）
onUnload(() => {
  // 注意：这里不清理currentProductId，因为用户可能只是去选择地址然后返回
  // 只有在真正离开页面时才清理
})
</script>

<style lang="scss" scoped>
.evaluate-detail-page {
  background: linear-gradient(180deg, #f8fffe 0%, #f0f9f8 50%, #e8f5f3 100%);
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.content-area {
  flex: 1;
  overflow: hidden;
  padding-bottom: 120rpx;
}

// 顶部卡片容器
.top-card-container {
  margin: 0 0 20rpx 0;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border-radius: 0;
  overflow: hidden;
  z-index: 1;
}

// 顶部提示栏
.top-notice {
  background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
  padding: 24rpx 24rpx 30rpx 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  .notice-text {
    color: #fff;
    font-size: 28rpx;
    font-weight: 600;
    letter-spacing: 0.5rpx;
  }

  .notice-right {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
    color: #fff;
    padding: 10rpx 20rpx;
    border-radius: 24rpx;
    font-size: 24rpx;
    font-weight: 600;
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10rpx);
  }
}

// 商品信息卡片
.product-card {
  background-color: #fff;
  padding: 24rpx;
  position: relative;
  z-index: 2;
  margin-top: -15rpx;
  border-radius: 16rpx 16rpx 0 0; 

  .product-info {
    display: flex;
    gap: 24rpx;
    margin-bottom: 24rpx;

    .product-image {
      position: relative;
      width: 140rpx;
      height: 140rpx;
      border-radius: 16rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

      .product-img {
        width: 100%;
        height: 100%;
        border-radius: 16rpx;
      }

      .image-placeholder {
        width: 100%;
        height: 100%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2rpx dashed #ced4da;

        .placeholder-text {
          font-size: 22rpx;
          color: #6c757d;
          font-weight: 500;
        }
      }
    }

    .product-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 12rpx;

      .product-name {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        line-height: 1.4;
      }

      .product-code {
        font-size: 28rpx;
        color: #666;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        font-weight: 600;
        margin-top: 8rpx;
        letter-spacing: 0.5rpx;
      }
    }
  }

  .price-options-container {
    margin-bottom: 24rpx;
  }

  .price-options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .options-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
    }

    .condition-explain-btn {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 8rpx 16rpx;
      background: rgba(13, 115, 119, 0.1);
      border-radius: 20rpx;
      border: 1rpx solid rgba(13, 115, 119, 0.2);

      .explain-icon {
        width: 24rpx;
        height: 24rpx;
        background: #0d7377;
        color: white;
        border-radius: 50%;
        font-size: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
      }

      .explain-text {
        font-size: 24rpx;
        color: #0d7377;
        font-weight: 500;
      }

      &:active {
        background: rgba(13, 115, 119, 0.15);
      }
    }
  }

  .price-options {
    display: flex;
    gap: 12rpx;

    .price-option {
      flex: 1;
      border: 2rpx solid #e5e5e5;
      border-radius: 12rpx;
      padding: 24rpx 16rpx;
      text-align: center;
      transition: all 0.2s ease;
      background: #ffffff;
      cursor: pointer;

      &:active:not(.disabled) {
        transform: scale(0.98);
      }

      &.active {
        border-color: #0d7377;
        background: linear-gradient(135deg, #f0fffe 0%, #e8f8f5 100%);

        .option-label {
          color: #0d7377;
          font-weight: 600;
        }

        .voucher-price {
          .voucher-label {
            color: #0d7377;
            font-weight: 600;
          }
          .voucher-amount {
            color: #333;
            font-weight: 700;
          }
        }

        .normal-price {
          color: #0d7377;
          font-weight: 600;
        }
      }

      &.disabled {
        opacity: 0.6;
        cursor: not-allowed;

        .option-price {
          color: #999;
        }
      }

      .option-label {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 16rpx;
        font-weight: 500;
      }

      .price-display {
        .voucher-price {
          display: flex;
          align-items: baseline;
          justify-content: center;
          gap: 4rpx;

          .voucher-label {
            font-size: 24rpx;
            color: #0d7377;
            font-weight: 500;
          }

          .voucher-amount {
            font-size: 36rpx;
            color: #333;
            font-weight: 700;
          }
        }

        .normal-price {
          font-size: 36rpx;
          color: #333;
          font-weight: 600;
        }
      }

      .loading-text {
        font-size: 24rpx;
        color: #999;
      }



      .voucher-badge {
        position: absolute;
        top: -8rpx;
        right: -8rpx;
        width: 32rpx;
        height: 32rpx;
        background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18rpx;
        color: #fff;
        font-weight: 600;
        box-shadow: 0 2rpx 8rpx rgba(13, 115, 119, 0.3);
        z-index: 2;
        animation: pulse 2s infinite;
      }
    }
  }

  // 价格明细样式
  .price-summary {
    display: flex;
    align-items: center;
    gap: 12rpx;
    margin-bottom: 16rpx;
    padding: 20rpx 24rpx;
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border-radius: 16rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
    border: 1rpx solid #e8f0ff;

    .summary-text {
      font-size: 28rpx;
      color: #666;
      font-weight: 500;
    }

    .voucher-addition {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 8rpx 12rpx;
      background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
      border-radius: 20rpx;
      border: 1rpx solid #ffebee;

      .plus-symbol {
        font-size: 24rpx;
        color: #ff4757;
        font-weight: 700;
      }

      .bonus-text {
        font-size: 28rpx;
        color: #ff4757;
        font-weight: 600;
      }

      .time-limit {
        padding: 4rpx 8rpx;
        background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
        border-radius: 8rpx;
        margin-left: 4rpx;

        .limit-text {
          font-size: 20rpx;
          color: #fff;
          font-weight: 600;
        }
      }
    }
  }

  // 优惠券选择按钮样式
  .voucher-select-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 24rpx;
    background: linear-gradient(135deg, #fff 0%, #f8fffe 100%);
    border-radius: 12rpx;
    margin-bottom: 24rpx;
    border: 2rpx solid #e8f8f5;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2rpx 8rpx rgba(13, 115, 119, 0.08);

    &:active {
      background: linear-gradient(135deg, #f0fffe 0%, #e8f8f5 100%);
      border-color: #0d7377;
      transform: scale(0.98);
    }

    .btn-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4rpx;

      .btn-title {
        font-size: 28rpx;
        color: #0d7377;
        font-weight: 600;
      }

      .btn-desc {
        font-size: 24rpx;
        color: #666;
        font-weight: 400;
      }
    }

    .btn-arrow {
      font-size: 24rpx;
      color: #0d7377;
      font-weight: 600;
    }
  }

  .warning-notice {
    display: flex;
    align-items: flex-start;
    gap: 12rpx;
    padding: 16rpx 0;
    margin-bottom: 1rpx;

    .warning-icon {
      font-size: 24rpx;
      color: #e6a23c;
      margin-top: 2rpx;
      opacity: 0.8;
    }

    .warning-text {
      flex: 1;
      font-size: 24rpx;
      color: #909399;
      line-height: 1.5;
      font-weight: 400;
    }
  }
}

// 三步路线
.process-section {
  background-color: #fff;
  margin: 20rpx 24rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .section-title {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }

  .process-steps {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .step-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12rpx;

      .step-icon {
        width: 60rpx;
        height: 60rpx;
        background-color: #f0f0f0;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
      }

      .step-text {
        font-size: 22rpx;
        color: #666;
        text-align: center;
      }
    }

    .step-arrow {
      font-size: 24rpx;
      color: #ccc;
      margin: 0 16rpx;
    }
  }
}

// 配送方式
.delivery-section {
  background-color: #fff;
  margin: 20rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;

  .delivery-options {
    display: flex;
    background-color: #f5f5f5;
    border-radius: 12rpx;
    padding: 8rpx;
    margin: 24rpx;

    .delivery-option {
      flex: 1;
      position: relative;
      padding: 20rpx 24rpx;
      background-color: transparent;
      border-radius: 8rpx;
      text-align: center;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;

      &:active {
        transform: scale(0.98);
      }

      &.active {
        background-color: #fff;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

        .option-name {
          color: #333;
          font-weight: 600;
        }

        .option-tag {
          background-color: #16a085;
          box-shadow: 0 1rpx 4rpx rgba(22, 160, 133, 0.3);
        }
      }

      .option-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8rpx;
      }

      .option-name {
        font-size: 28rpx;
        color: #666;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      .option-tag {
        background-color: #16a085;
        padding: 0 4rpx;
        border-radius: 6rpx;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        height: 30rpx;

        .tag-text {
          color: #fff;
          font-size: 18rpx;
          font-weight: 600;
          line-height: 1;
        }
      }
    }
  }

  .pickup-content, .self-content {
    .address-item, .time-item, .express-item, .tracking-item {
      display: flex;
      align-items: center;
      padding: 32rpx 24rpx;
      border-bottom: 1rpx solid #e9ecef;
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background-color: #f8f9fa;
      }

      &.disabled {
        opacity: 0.6;
        cursor: not-allowed;

        &:active {
          background-color: transparent;
        }
      }

      .address-icon, .time-icon, .express-icon, .tracking-icon {
        margin-right: 20rpx;
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        svg {
          width: 32rpx;
          height: 32rpx;
        }

        &.orange-bg {
          background-color: #16a085;
          border-radius: 50%;

          .address-text-icon {
            color: #fff;
            font-size: 24rpx;
            font-weight: 600;
          }
        }
      }

      .address-text, .time-text, .express-text, .tracking-text {
        flex: 1;
        font-size: 30rpx;
        color: #212529;
        font-weight: 500;
      }

      .address-action, .time-action, .express-action, .tracking-action {
        font-size: 26rpx;
        color: #6c757d;
        font-weight: 500;

        &.disabled {
          color: #999;
        }
      }

      .address-content {
        flex: 1;
        margin-right: 20rpx;

        .selected-address {
          .address-name {
            font-size: 28rpx;
            color: #333;
            font-weight: 600;
            margin-bottom: 8rpx;
            display: block;
          }

          .address-detail {
            font-size: 24rpx;
            color: #666;
            line-height: 1.4;
            display: block;
          }
        }
      }

      .tracking-input {
        flex: 1;
        font-size: 26rpx;
        color: #333;
        text-align: right;
        background: transparent;
        border: none;
        outline: none;

        &::placeholder {
          color: #999;
        }
      }

      .divider-line {
        width: 1rpx;
        height: 32rpx;
        background-color: #e9ecef;
        margin: 0 16rpx;
      }

      .tracking-actions {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .tracking-action {
          font-size: 26rpx;
          color: #6c757d;
          font-weight: 500;
        }

        .scan-btn {
          padding: 8rpx;
          background-color: #f8f9fa;
          border-radius: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          &:active {
            background-color: #e9ecef;
          }

          svg {
            width: 20rpx;
            height: 20rpx;
          }
        }
      }

      .address-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8rpx;

        .address-name {
          font-size: 30rpx;
          color: #212529;
          font-weight: 500;
        }

        .address-detail {
          font-size: 24rpx;
          color: #6c757d;
          line-height: 1.4;
        }
      }

      .copy-btn {
        background-color: #16a085;
        color: #fff;
        padding: 12rpx 24rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 500;
      }
    }
  }
}

// 底部操作
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 2rpx 20rpx;
  box-shadow: 0 -4rpx 24rpx rgba(0, 0, 0, 0.08);
  z-index: 100;
  display: flex;
  align-items: center;
  gap: 24rpx;
  border-top: 1rpx solid #e9ecef;

  .cart-section {
    .cart-icon {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8rpx;
      position: relative;
      padding: 12rpx;
      border-radius: 16rpx;
      transition: all 0.3s ease;

      &:active {
        background-color: rgba(0, 0, 0, 0.05);
        transform: scale(0.95);
      }

      .cart-badge {
        position: absolute;
        top: 4rpx;
        right: 4rpx;
        background: linear-gradient(135deg, #ff4757 0%, #ff6b7a 100%);
        color: #fff;
        font-size: 18rpx;
        font-weight: 700;
        padding: 4rpx 8rpx;
        border-radius: 50rpx;
        min-width: 32rpx;
        height: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
      }

      svg {
        width: 52rpx;
        height: 52rpx;
        color: #495057;
      }

      .cart-text {
        font-size: 22rpx;
        color: #6c757d;
        font-weight: 500;
      }
    }
  }

  .submit-button {
    flex: 1;
    background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
    color: #fff;
    padding: 15rpx 18rpx;
    border-radius: 50rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: 700;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 6rpx 24rpx rgba(22, 160, 133, 0.35);

    &.submitting {
      background: linear-gradient(135deg, #999 0%, #bbb 100%);
      box-shadow: 0 6rpx 24rpx rgba(153, 153, 153, 0.35);
      pointer-events: none;
    }
    position: relative;
    overflow: hidden;

    // 添加光泽效果
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s ease;
    }

    &:active {
      transform: scale(0.98) translateY(2rpx);
      background: linear-gradient(135deg, #0a5d61 0%, #117a65 100%);
      box-shadow: 0 4rpx 16rpx rgba(13, 115, 119, 0.45);

      &::before {
        left: 100%;
      }
    }

    // 悬停效果（虽然移动端不常用，但为了完整性）
    &:hover::before {
      left: 100%;
    }

    .submit-text {
      display: block;
      letter-spacing: 2rpx;
      text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
    }
  }
}

// 加价券使用提示弹窗
.voucher-tip-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;

  .tip-modal-content {
    background-color: #fff;
    border-radius: 16rpx;
    width: 100%;
    max-width: 520rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
    margin: 0 auto;

    .tip-content {
      padding: 40rpx 32rpx 32rpx;
      text-align: center;

      .tip-icon {
        width: 80rpx;
        height: 80rpx;
        background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
        border-radius: 50%;
        margin: 0 auto 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .icon-text {
          font-size: 40rpx;
          line-height: 1;
          color: #fff;
        }
      }

      .tip-title {
        display: block;
        font-size: 32rpx;
        color: #333;
        font-weight: 600;
        margin-bottom: 16rpx;
        line-height: 1.4;
      }

      .tip-message {
        display: block;
        font-size: 26rpx;
        color: #666;
        line-height: 1.6;
        font-weight: 400;
      }
    }

    .tip-actions {
      padding: 0 24rpx 24rpx;

      .tip-btn {
        width: 100%;
        padding: 20rpx 16rpx;
        background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
        border-radius: 12rpx;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s ease;
        box-sizing: border-box;

        &:active {
          background: linear-gradient(135deg, #0a5d61 0%, #117a65 100%);
          transform: scale(0.98);
        }

        .btn-text {
          font-size: 28rpx;
          color: #fff;
          font-weight: 600;
          display: block;
          width: 100%;
          text-align: center;
        }
      }
    }
  }
}

// 加价券弹窗
.coupon-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;

  .coupon-modal-content {
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .coupon-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 32rpx 24rpx;
      border-bottom: 1rpx solid #f0f0f0;
      background: linear-gradient(135deg, #f0fffe 0%, #ffffff 100%);

      .coupon-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .coupon-close-btn {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 50%;
        transition: all 0.3s ease;

        &:active {
          background-color: rgba(0, 0, 0, 0.1);
          transform: scale(0.95);
        }
      }
    }

    .coupon-container {
      flex: 1;
      overflow-y: auto;
      padding: 24rpx 32rpx;

      .loading-state {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 80rpx 40rpx;

        .loading-text {
          font-size: 28rpx;
          color: #666;
        }
      }

      .coupon-list {
        display: flex;
        flex-direction: column;
        gap: 20rpx;

        .coupon-card {
          display: flex;
          align-items: center;
          padding: 32rpx 24rpx;
          background: linear-gradient(135deg, #fff 0%, #fafafa 100%);
          border: 2rpx solid #e9ecef;
          border-radius: 20rpx;
          position: relative;
          transition: all 0.3s ease;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            left: 140rpx;
            top: 20rpx;
            bottom: 20rpx;
            width: 2rpx;
            background: repeating-linear-gradient(
              to bottom,
              #e9ecef 0,
              #e9ecef 8rpx,
              transparent 8rpx,
              transparent 16rpx
            );
          }

          &.selected {
            border-color: #16a085;
            background: linear-gradient(135deg, #f0fffe 0%, #e8f8f5 100%);
            box-shadow: 0 8rpx 24rpx rgba(22, 160, 133, 0.15);
            transform: translateY(-2rpx);
          }

          &.disabled {
            opacity: 0.5;
            background: linear-gradient(135deg, #f5f5f5 0%, #e9e9e9 100%);
            border-color: #ddd;
            cursor: not-allowed;

            .coupon-amount {
              color: #999;
            }

            .coupon-condition {
              background-color: rgba(153, 153, 153, 0.1);
              color: #999;
            }
          }

          &:active:not(.disabled) {
            transform: scale(0.98);
          }

          .coupon-left-section {
            width: 120rpx;
            text-align: center;
            padding-right: 24rpx;

            .coupon-amount {
              font-size: 40rpx;
              color: #16a085;
              font-weight: 700;
              display: block;
              margin-bottom: 8rpx;
              text-shadow: 0 1rpx 2rpx rgba(22, 160, 133, 0.2);
            }

            .coupon-condition {
              font-size: 22rpx;
              color: #666;
              display: block;
              background-color: rgba(255, 107, 53, 0.1);
              padding: 4rpx 8rpx;
              border-radius: 12rpx;
            }
          }

          .coupon-right-section {
            flex: 1;
            padding-left: 24rpx;

            .coupon-name {
              font-size: 28rpx;
              color: #333;
              font-weight: 600;
              margin-bottom: 12rpx;
              display: block;
            }

            .coupon-desc {
              font-size: 24rpx;
              color: #666;
              margin-bottom: 8rpx;
              display: block;
            }

            .coupon-benefit {
              margin-bottom: 8rpx;

              .benefit-text {
                font-size: 24rpx;
                color: #0d7377;
                font-weight: 600;
                background: rgba(13, 115, 119, 0.1);
                padding: 4rpx 8rpx;
                border-radius: 8rpx;
                display: inline-block;
              }
            }

            .coupon-time {
              font-size: 22rpx;
              color: #999;
              display: block;
            }

            .coupon-disabled-reason {
              font-size: 22rpx;
              color: #ff4757;
              margin-top: 8rpx;
              display: block;
            }
          }

          .coupon-select-icon {
            position: absolute;
            top: 16rpx;
            right: 16rpx;
            width: 32rpx;
            height: 32rpx;
            background-color: #16a085;
            color: #fff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2rpx 8rpx rgba(22, 160, 133, 0.3);
          }
        }
      }

      .empty-coupon {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 80rpx 40rpx;
        text-align: center;

        .empty-icon {
          margin-bottom: 32rpx;
          opacity: 0.6;
        }

        .empty-text {
          font-size: 32rpx;
          color: #666;
          font-weight: 500;
          margin-bottom: 16rpx;
        }

        .empty-desc {
          font-size: 26rpx;
          color: #999;
          line-height: 1.5;
        }
      }
    }

    .coupon-footer {
      padding: 24rpx 32rpx 32rpx;
      border-top: 1rpx solid #f0f0f0;
      background-color: #fafafa;

      .selected-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16rpx 20rpx;
        margin-bottom: 16rpx;
        background: rgba(13, 115, 119, 0.1);
        border-radius: 16rpx;
        border: 1rpx solid rgba(13, 115, 119, 0.2);

        .selected-text {
          font-size: 26rpx;
          color: #333;
          font-weight: 500;
        }

        .selected-benefit {
          font-size: 28rpx;
          color: #0d7377;
          font-weight: 700;
        }
      }

      .coupon-confirm-btn {
        background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
        color: #fff;
        padding: 24rpx;
        border-radius: 50rpx;
        text-align: center;
        font-size: 28rpx;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 6rpx 20rpx rgba(255, 107, 53, 0.3);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.6s ease;
        }

        &:active {
          background: linear-gradient(135deg, #0a5d61 0%, #117a65 100%);
          transform: scale(0.98);
          box-shadow: 0 4rpx 16rpx rgba(13, 115, 119, 0.4);

          &::before {
            left: 100%;
          }
        }

        .coupon-confirm-text {
          text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}

// 快递公司选择弹窗
.express-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;

  .modal-content {
    width: 100%;
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    padding: 0;
    max-height: 60vh;
    overflow: hidden;

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 32rpx 24rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .modal-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 36rpx;
        color: #999;
        cursor: pointer;
      }
    }

    .express-list {
      max-height: 400rpx;
      overflow-y: auto;

      .express-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 32rpx;
        border-bottom: 1rpx solid #f8f9fa;
        transition: all 0.3s ease;

        &:active {
          background-color: #f8f9fa;
        }

        &:last-child {
          border-bottom: none;
        }

        .express-name {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }

        .express-check {
          color: #16a085;
          font-size: 24rpx;
          font-weight: 600;
        }
      }
    }
  }
}

// 右侧固定回收标准按钮
.fixed-standard-btn {
  position: fixed;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(180deg, #0d7377 0%, #14a085 100%);
  border-radius: 20rpx 0 0 20rpx;
  padding: 20rpx 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  box-shadow: -2rpx 0 12rpx rgba(13, 115, 119, 0.4);
  z-index: 999;

  .fixed-standard-text {
    color: #fff;
    font-size: 24rpx;
    font-weight: 600;
    writing-mode: vertical-lr;
    text-orientation: upright;
  }
}

// 回收标准弹窗样式
.standard-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .standard-modal-content {
    background-color: #fff;
    border-radius: 20rpx;
    width: 90%;
    max-width: 600rpx;
    max-height: 80vh;
    overflow-y: auto;

    .standard-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 32rpx 20rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .standard-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 36rpx;
        color: #999;
        background-color: #f5f5f5;
        border-radius: 50%;
      }
    }

    .standard-subtitle {
      padding: 20rpx 32rpx;
      font-size: 26rpx;
      color: #666;
      text-align: center;
    }

    .standard-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20rpx;
      padding: 20rpx 32rpx;

      .standard-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12rpx;

        .standard-image {
          width: 120rpx;
          height: 120rpx;
          border-radius: 12rpx;
          overflow: hidden;

          .standard-img {
            width: 100%;
            height: 100%;
            border-radius: 12rpx;
          }

          .standard-placeholder {
            width: 100%;
            height: 100%;
            background-color: #e9ecef;
            border-radius: 12rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2rpx dashed #ced4da;

            .placeholder-text {
              font-size: 20rpx;
              color: #6c757d;
              font-weight: 500;
            }
          }
        }

        .standard-label {
          font-size: 22rpx;
          color: #333;
          text-align: center;
        }
      }
    }

    .standard-checkbox {
      padding: 20rpx 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .checkbox-text {
        font-size: 24rpx;
        color: #666;
      }
    }

    .standards-loading {
      padding: 60rpx 32rpx;
      text-align: center;

      .loading-text {
        color: #666;
        font-size: 26rpx;
      }
    }

    .no-standards {
      padding: 60rpx 32rpx;
      text-align: center;

      .no-standards-text {
        color: #999;
        font-size: 26rpx;
      }
    }

    .standard-buttons {
      display: flex;
      gap: 20rpx;
      margin: 20rpx 32rpx 32rpx;
    }

    .photo-estimate-btn {
      flex: 1;
      background: transparent;
      border: 2rpx solid #000;
      border-radius: 12rpx;
      padding: 22rpx; // 减少padding以补偿border
      text-align: center;
      transition: all 0.3s ease;

      &:active {
        background: rgba(0, 0, 0, 0.05);
        transform: scale(0.98);
      }

      .photo-estimate-text {
        color: #000;
        font-size: 28rpx;
        font-weight: 600;
      }
    }

    .standard-confirm-btn {
      flex: 1;
      background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
      border-radius: 12rpx;
      padding: 24rpx;
      text-align: center;
      box-shadow: 0 8rpx 20rpx rgba(13, 115, 119, 0.3);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 4rpx 10rpx rgba(13, 115, 119, 0.4);
      }

      .confirm-text {
        color: #fff;
        font-size: 28rpx;
        font-weight: 600;
        text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 时间选择弹窗样式
.time-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;

  .time-modal-content {
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    width: 100%;
    max-height: 70vh;
    overflow-y: auto;

    .time-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 32rpx 20rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .time-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 36rpx;
        color: #999;
        background-color: #f5f5f5;
        border-radius: 50%;
      }
    }

    .time-container {
      display: flex;
      height: 500rpx;

      // 左侧日期选择
      .date-sidebar {
        width: 200rpx;
        background-color: #f8f9fa;
        border-right: 1rpx solid #e9ecef;

        .date-item {
          padding: 32rpx 24rpx;
          border-bottom: 1rpx solid #e9ecef;
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;

          &.active {
            background-color: #fff;
            border-right: 4rpx solid #16a085;

            .date-name {
              color: #16a085;
              font-weight: 600;
            }

            .date-desc {
              color: #16a085;
            }

            &::before {
              content: '';
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 0;
              height: 0;
              border-left: 8rpx solid #ff6b35;
              border-top: 8rpx solid transparent;
              border-bottom: 8rpx solid transparent;
            }
          }

          &:active {
            transform: scale(0.98);
          }

          .date-name {
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
            display: block;
            margin-bottom: 8rpx;
          }

          .date-desc {
            font-size: 24rpx;
            color: #666;
            display: block;
          }
        }
      }

      // 右侧时间选择
      .time-content {
        flex: 1;
        padding: 32rpx 24rpx;
        overflow-y: auto;

        .time-slots {
          display: flex;
          flex-direction: column;
          gap: 20rpx;

          .time-slot {
            padding: 24rpx 20rpx;
            border: 2rpx solid #e9ecef;
            border-radius: 12rpx;
            background-color: #fff;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;

            &.urgent {
              background: linear-gradient(135deg, #f0fffe 0%, #e8f8f5 100%);
              border-color: #16a085;

              .slot-title {
                color: #16a085;
                font-weight: 600;
              }

              .slot-desc {
                color: #16a085;
              }

              &::after {
                content: '推荐';
                position: absolute;
                top: -8rpx;
                right: 16rpx;
                background-color: #16a085;
                color: #fff;
                font-size: 20rpx;
                padding: 4rpx 12rpx;
                border-radius: 12rpx;
              }
            }

            &.active {
              border-color: #16a085;
              background-color: #f0fffe;
              box-shadow: 0 4rpx 16rpx rgba(22, 160, 133, 0.15);

              .slot-title {
                color: #16a085;
                font-weight: 600;
              }

              &.urgent {
                background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);

                .slot-title, .slot-desc {
                  color: #fff;
                }

                &::after {
                  background-color: #fff;
                  color: #16a085;
                }
              }
            }

            &:active {
              transform: scale(0.98);
            }

            .slot-title {
              font-size: 28rpx;
              color: #333;
              font-weight: 500;
              display: block;
              margin-bottom: 4rpx;
            }

            .slot-desc {
              font-size: 24rpx;
              color: #666;
              display: block;
            }
          }
        }
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(13, 115, 119, 0.3);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 4rpx 16rpx rgba(13, 115, 119, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(13, 115, 119, 0.3);
  }
}

// 成色说明弹窗样式
.condition-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;

  .condition-modal-content {
    background: #fff;
    border-radius: 24rpx 24rpx 0 0;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideUp 0.3s ease-out;
  }

  .condition-header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 24rpx 32rpx;
    border-bottom: 1rpx solid #f0f0f0;
    position: relative;

    .condition-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      position: absolute;
      right: 32rpx;
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36rpx;
      color: #999;
      background: #f8f8f8;
      border-radius: 50%;

      &:active {
        background: #e8e8e8;
      }
    }
  }

  .condition-content {
    padding: 24rpx 32rpx;
    max-height: 60vh;
    overflow-y: auto;

    .condition-item {
      margin-bottom: 32rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .condition-item-header {
        margin-bottom: 16rpx;

        .condition-badge {
          display: inline-block;
          padding: 8rpx 16rpx;
          border-radius: 16rpx;
          font-size: 24rpx;
          font-weight: 600;
          color: #fff;

          &.new {
            background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
          }

          &.light {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
          }

          &.obvious {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
          }
        }
      }

      .condition-item-body {
        .condition-description {
          margin-bottom: 16rpx;

          .condition-text {
            display: block;
            font-size: 26rpx;
            color: #666;
            line-height: 1.6;
          }
        }

        .condition-images {
          display: flex;
          flex-wrap: wrap;
          gap: 12rpx;

          .condition-img {
            width: 180rpx;
            height: 180rpx;
            border-radius: 12rpx;
            background: #f5f5f5;
          }
        }
      }
    }
  }

  .condition-footer {
    padding: 24rpx 32rpx 32rpx;
    border-top: 1rpx solid #f0f0f0;

    .condition-note {
      background: #f8f9fa;
      border-radius: 12rpx;
      padding: 16rpx;
      margin-bottom: 24rpx;

      .note-text {
        font-size: 24rpx;
        color: #666;
        line-height: 1.5;
      }
    }

    .condition-confirm-btn {
      width: 100%;
      height: 88rpx;
      background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .confirm-text {
        font-size: 28rpx;
        color: #fff;
        font-weight: 600;
      }

      &:active {
        opacity: 0.8;
      }
    }
  }
}

@keyframes modalSlideUp {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}
</style>
