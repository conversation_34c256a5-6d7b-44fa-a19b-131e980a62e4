<template>
  <view class="service-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="nav-left" @click="goBack">
          <text class="iconfont iconfanhui1"></text>
        </view>
        <view class="nav-title">咨询客服</view>
        <view class="nav-right">
          <text class="nav-dot">•••</text>
          <text class="nav-refresh">⟲</text>
        </view>
      </view>
    </view>

    <!-- 客服信息卡片 -->
    <view class="service-card">
      <view class="qr-code-container">
        <view class="qr-code-frame">
          <image
            class="qr-code-image"
            src="/static/images/service-qr.png"
            mode="aspectFit"
            @error="onImageError"
          />
          <!-- 图片加载失败时的占位符 -->
          <view class="qr-placeholder" v-if="imageError">
            <view class="placeholder-icon">📱</view>
            <text class="placeholder-text">二维码</text>
          </view>
        </view>
      </view>
      <view class="service-info">
        <view class="service-title">
          <view class="service-icon-wrapper">
            <text class="service-icon">✓</text>
          </view>
          <text class="service-name">1对1微信客服</text>
        </view>
        <view class="service-desc">长按识别二维码添加</view>
      </view>
    </view>

    <!-- 工作时间提示 -->
    <view class="work-time-notice">
      人工客服工作时间为8:00-20:00，咨询高峰期会有延迟
    </view>

    <!-- 热门问题 -->
    <view class="faq-section">
      <view class="section-title">热门问题</view>
      <view class="faq-list">
        <view 
          class="faq-item" 
          v-for="(item, index) in faqList" 
          :key="index"
          @click="toggleFaq(index)"
        >
          <view class="faq-question">
            <text class="question-number">{{ index + 1 }}.</text>
            <text class="question-text">{{ item.question }}</text>
            <text class="expand-icon" :class="{ 'expanded': item.expanded }">
              <text class="iconfont iconjiantouarrow492"></text>
            </text>
          </view>
          <view class="faq-answer" v-if="item.expanded">
            {{ item.answer }}
          </view>
        </view>
      </view>
    </view>

    <!-- 底部联系方式 -->
    <view class="contact-bottom">
      <view class="contact-item" @click="makePhoneCall">
        <text class="contact-icon">📞</text>
        <text class="contact-text">电话客服</text>
      </view>
      <view class="contact-item" @click="openOnlineService">
        <text class="contact-icon">💬</text>
        <text class="contact-text">在线客服</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      imageError: false,
      faqList: [
        {
          question: '哪些城市/地区支持回收服务？',
          answer: '您好，品牌鞋服回收暂时只开放了江浙沪地区，潮鞋潮服全国可回收。',
          expanded: true
        },
        {
          question: '订单提交后能取消吗？',
          answer: '订单提交后24小时内可以取消，超过24小时需要联系客服处理。',
          expanded: false
        },
        {
          question: '不同意报价，衣服怎么退回？',
          answer: '如果您不同意我们的报价，我们会免费为您寄回商品，无需承担任何费用。',
          expanded: false
        },
        {
          question: '我的衣服有轻微破损，还能回收吗？',
          answer: '轻微破损的衣服我们也可以回收，但价格会根据破损程度进行相应调整。',
          expanded: false
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    toggleFaq(index) {
      this.faqList[index].expanded = !this.faqList[index].expanded
    },
    makePhoneCall() {
      uni.makePhoneCall({
        phoneNumber: '************'
      })
    },
    openOnlineService() {
      // 打开在线客服
      uni.showToast({
        title: '正在连接客服...',
        icon: 'loading'
      })
    },

    onImageError() {
      this.imageError = true;
    },

    // 生成二维码模块状态
    getModuleState(row, col) {
      // 简单的伪随机算法生成二维码模块
      const seed = (row * 21 + col) * 7;
      return (seed % 3) !== 0;
    }
  }
}
</script>

<style lang="scss" scoped>
.service-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #4A90E2 0%, #7B68EE 50%, #9370DB 100%);
  padding-bottom: 120rpx;
}

.custom-navbar {
  padding-top: var(--status-bar-height);
  background: transparent;
  
  .navbar-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx;
    
    .nav-left, .nav-right {
      display: flex;
      align-items: center;
      gap: 24rpx;

      .iconfont {
        font-size: 36rpx;
        color: #fff;
      }

      .nav-dot, .nav-refresh {
        font-size: 36rpx;
        color: #fff;
        font-weight: bold;
      }
    }
    
    .nav-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #fff;
    }
  }
}

.service-card {
  margin: 60rpx 32rpx 40rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  
  .qr-code-container {
    margin-bottom: 40rpx;

    .qr-code-frame {
      width: 400rpx;
      height: 400rpx;
      background: #fff;
      border-radius: 24rpx;
      padding: 24rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
      position: relative;

      .qr-code-image {
        width: 100%;
        height: 100%;
        border-radius: 12rpx;
      }

      .qr-placeholder {
        position: absolute;
        top: 24rpx;
        left: 24rpx;
        right: 24rpx;
        bottom: 24rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border-radius: 12rpx;
        border: 2rpx dashed #ddd;

        .placeholder-icon {
          font-size: 80rpx;
          margin-bottom: 16rpx;
          opacity: 0.6;
        }

        .placeholder-text {
          font-size: 28rpx;
          color: #999;
        }
      }
    }
  }
  
  .service-info {
    text-align: center;

    .service-title {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16rpx;

      .service-icon-wrapper {
        width: 40rpx;
        height: 40rpx;
        background: #4CAF50;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12rpx;

        .service-icon {
          font-size: 24rpx;
          color: #fff;
          font-weight: bold;
        }
      }

      .service-name {
        font-size: 36rpx;
        font-weight: 600;
        color: #333;
      }
    }

    .service-desc {
      font-size: 28rpx;
      color: #666;
    }
  }
}

.work-time-notice {
  margin: 0 32rpx 60rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

.faq-section {
  margin: 0 32rpx;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  
  .section-title {
    padding: 32rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    border-bottom: 2rpx solid #4A90E2;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -2rpx;
      left: 32rpx;
      width: 60rpx;
      height: 4rpx;
      background: #4A90E2;
    }
  }
  
  .faq-list {
    .faq-item {
      border-bottom: 1rpx solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .faq-question {
        padding: 32rpx;
        display: flex;
        align-items: center;
        cursor: pointer;
        
        .question-number {
          font-size: 28rpx;
          color: #4A90E2;
          font-weight: 600;
          margin-right: 12rpx;
        }
        
        .question-text {
          flex: 1;
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }
        
        .expand-icon {
          transition: transform 0.3s ease;
          
          .iconfont {
            font-size: 24rpx;
            color: #999;
          }
          
          &.expanded {
            transform: rotate(180deg);
          }
        }
      }
      
      .faq-answer {
        padding: 0 32rpx 32rpx 76rpx;
        font-size: 26rpx;
        color: #666;
        line-height: 1.6;
        background: #f8f9fa;
      }
    }
  }
}

.contact-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  display: flex;
  border-top: 1rpx solid #f0f0f0;
  
  .contact-item {
    flex: 1;
    padding: 32rpx;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12rpx;
    
    &:first-child {
      border-right: 1rpx solid #f0f0f0;
    }
    
    &:active {
      background: #f8f9fa;
    }
    
    .contact-icon {
      font-size: 48rpx;
    }
    
    .contact-text {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
  }
}
</style>
