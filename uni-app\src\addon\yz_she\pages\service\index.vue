<template>
  <view class="service-page">
    <!-- 二维码区域 -->
    <view class="qr-section">
      <view class="qr-container">
        <view class="qr-frame">
          <!-- 二维码占位符 -->
          <view class="qr-placeholder">
            <text class="placeholder-text">XX</text>
          </view>
        </view>
      </view>

      <!-- 客服信息 -->
      <view class="service-info">
        <view class="service-title">
          <text class="service-icon">😊</text>
          <text class="service-text">1对1微信客服</text>
        </view>
        <view class="service-desc">长按识别二维码添加</view>
      </view>
    </view>

    <!-- 工作时间提示 -->
    <view class="work-time">
      人工客服工作时间为8:00-20:00，咨询高峰期会有延迟
    </view>

    <!-- 热门问题 -->
    <view class="faq-section">
      <view class="faq-title">
        <text>热门问题</text>
        <view class="title-underline"></view>
      </view>

      <view class="faq-list">
        <view
          class="faq-item"
          v-for="(item, index) in faqList"
          :key="index"
          @click="toggleFaq(index)"
        >
          <view class="faq-question">
            <text class="question-number">{{ index + 1 }}.</text>
            <text class="question-text">{{ item.question }}</text>
            <text class="expand-icon" :class="{ 'expanded': item.expanded }">
              {{ item.expanded ? '▲' : '▼' }}
            </text>
          </view>
          <view class="faq-answer" v-if="item.expanded">
            {{ item.answer }}
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-buttons">
      <view class="btn-item" @click="makePhoneCall">
        <text class="btn-icon">💬</text>
        <text class="btn-text">电话客服</text>
      </view>
      <view class="btn-item" @click="openOnlineService">
        <text class="btn-icon">💬</text>
        <text class="btn-text">在线客服</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      faqList: [
        {
          question: '哪些城市/地区支持回收服务？',
          answer: '您好，品牌鞋服回收暂时只开放了江浙沪地区，潮鞋潮服全国可回收。',
          expanded: true
        },
        {
          question: '订单提交后能取消吗？',
          answer: '订单提交后24小时内可以取消，超过24小时需要联系客服处理。',
          expanded: false
        },
        {
          question: '不同意报价，衣服怎么退回？',
          answer: '如果您不同意我们的报价，我们会免费为您退回衣服，退回时间为3-5个工作日。',
          expanded: false
        },
        {
          question: '我的衣服有轻微瑕疵，还能回收吗？',
          answer: '轻微瑕疵的衣服我们也可以回收，但价格会根据瑕疵程度进行相应调整。',
          expanded: false
        }
      ]
    }
  },
  methods: {
    toggleFaq(index) {
      this.faqList[index].expanded = !this.faqList[index].expanded
    },

    makePhoneCall() {
      uni.makePhoneCall({
        phoneNumber: '************'
      })
    },

    openOnlineService() {
      uni.showToast({
        title: '正在连接在线客服...',
        icon: 'loading'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.service-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #5B9BD5 0%, #87CEEB 100%);
  padding: 60rpx 40rpx 160rpx 40rpx;
}

.qr-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;

  .qr-container {
    margin-bottom: 40rpx;

    .qr-frame {
      width: 500rpx;
      height: 500rpx;
      background: #fff;
      border-radius: 40rpx;
      padding: 40rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

      .qr-placeholder {
        width: 100%;
        height: 100%;
        background: #f8f8f8;
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .placeholder-text {
          font-size: 80rpx;
          color: #ccc;
          font-weight: bold;
        }
      }
    }
  }

  .service-info {
    text-align: center;

    .service-title {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16rpx;

      .service-icon {
        font-size: 40rpx;
        margin-right: 12rpx;
      }

      .service-text {
        font-size: 40rpx;
        font-weight: 600;
        color: #333;
      }
    }

    .service-desc {
      font-size: 32rpx;
      color: #666;
    }
  }
}

.work-time {
  margin-bottom: 40rpx;
  padding: 32rpx 40rpx;
  background: #fff;
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.faq-section {
  background: #fff;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

  .faq-title {
    padding: 40rpx 40rpx 20rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    position: relative;

    .title-underline {
      position: absolute;
      bottom: 8rpx;
      left: 40rpx;
      width: 60rpx;
      height: 4rpx;
      background: #5B9BD5;
      border-radius: 2rpx;
    }
  }

  .faq-list {
    .faq-item {
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .faq-question {
        padding: 32rpx 40rpx;
        display: flex;
        align-items: flex-start;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:active {
          background-color: #f8f9fa;
        }

        .question-number {
          font-size: 28rpx;
          color: #5B9BD5;
          font-weight: 600;
          margin-right: 12rpx;
          min-width: 40rpx;
          line-height: 1.5;
        }

        .question-text {
          flex: 1;
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
          line-height: 1.5;
        }

        .expand-icon {
          font-size: 20rpx;
          color: #999;
          transition: transform 0.3s ease;
          margin-top: 6rpx;

          &.expanded {
            transform: rotate(180deg);
          }
        }
      }

      .faq-answer {
        padding: 0 40rpx 32rpx 92rpx;
        font-size: 26rpx;
        line-height: 1.6;
        color: #666;
        animation: fadeIn 0.3s ease;
      }
    }
  }
}

.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: #fff;
  border-top: 1rpx solid #e8e8e8;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);

  .btn-item {
    flex: 1;
    padding: 36rpx 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:active {
      background-color: #f8f9fa;
    }

    &:first-child {
      border-right: 1rpx solid #e8e8e8;
    }

    .btn-icon {
      font-size: 36rpx;
      margin-bottom: 12rpx;
    }

    .btn-text {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>